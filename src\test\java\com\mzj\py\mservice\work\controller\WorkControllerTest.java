package com.mzj.py.mservice.work.controller;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.mzj.py.commons.ResultBean;
import com.mzj.py.commons.TokenRedisVo;
import com.mzj.py.commons.exception.CustomException;
import com.mzj.py.mservice.oss.service.OSSService;
import com.mzj.py.mservice.work.service.WorkService;
import com.mzj.py.mservice.work.vo.WorkInfoVo;
import com.mzj.py.mservice.work.vo.WorkPageParam;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.http.MediaType;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.setup.MockMvcBuilders;
import com.mzj.py.config.exception.GlobalExceptionHandler;

import java.io.File;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.*;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.*;

/**
 * WorkController单元测试类
 * 测试WorkController中所有REST端点的HTTP请求/响应处理
 */
@ExtendWith(MockitoExtension.class)
@DisplayName("WorkController单元测试")
class WorkControllerTest {

        private MockMvc mockMvc;

        @Mock
        private WorkService workService;

        @Mock
        private OSSService ossService;

        @InjectMocks
        private WorkController workController;

        private ObjectMapper objectMapper;
        private TokenRedisVo mockUser;
        private WorkInfoVo mockWorkInfoVo;
        private WorkPageParam mockWorkPageParam;
        private Map<String, Object> mockListResult;

        @BeforeEach
        void setUp() {
                objectMapper = new ObjectMapper();
                workController = spy(workController);
                mockMvc = MockMvcBuilders.standaloneSetup(workController)
                                .setControllerAdvice(new GlobalExceptionHandler())
                                .build();

                // 初始化测试数据
                mockUser = new TokenRedisVo();
                mockUser.setId(1L);
                mockUser.setPhone("13800138000");
                mockUser.setNicknames("测试用户");

                mockWorkInfoVo = new WorkInfoVo();
                mockWorkInfoVo.setId(1L);
                mockWorkInfoVo.setTitle("测试作品");
                mockWorkInfoVo.setContent("测试内容");
                mockWorkInfoVo.setFileUrl("http://test.com/audio.mp3");
                mockWorkInfoVo.setUserId(1L);
                mockWorkInfoVo.setShopId(1L);

                mockWorkPageParam = new WorkPageParam();
                mockWorkPageParam.setPageNumber(0);
                mockWorkPageParam.setPageSize(10);
                mockWorkPageParam.setKeyword("测试");

                mockListResult = new HashMap<>();
                mockListResult.put("total", 1);
                mockListResult.put("result", java.util.Arrays.asList(mockWorkInfoVo));

                // Mock ApiBaseController methods
                lenient().doReturn(1L).when(workController).getShopId(anyString());
                lenient().doReturn(mockUser).when(workController).getUser(anyString());
        }

        @Test
        @DisplayName("作品列表查询_成功场景")
        void testList_Success() throws Exception {
                // Given
                String accessToken = "valid_token";
                when(workService.list(accessToken, 0, 10, "测试"))
                                .thenReturn(ResultBean.successfulResult(mockListResult));

                // When & Then
                mockMvc.perform(get("/work/list")
                                .header("accessToken", accessToken)
                                .param("pageNumber", "0")
                                .param("pageSize", "10")
                                .param("keyword", "测试"))
                                .andExpect(status().isOk())
                                .andExpect(jsonPath("$.code").value("10000"))
                                .andExpect(jsonPath("$.resultData.total").value(1));

                verify(workService).list(accessToken, 0, 10, "测试");
        }

        @Test
        @DisplayName("作品列表查询_默认参数场景")
        void testList_DefaultParams() throws Exception {
                // Given
                String accessToken = "valid_token";
                when(workService.list(accessToken, 0, 10, null))
                                .thenReturn(ResultBean.successfulResult(mockListResult));

                // When & Then
                mockMvc.perform(get("/work/list")
                                .header("accessToken", accessToken))
                                .andExpect(status().isOk())
                                .andExpect(jsonPath("$.code").value("10000"));

                verify(workService).list(accessToken, 0, 10, null);
        }

        @Test
        @DisplayName("作品列表查询_自定义分页参数场景")
        void testList_CustomPagination() throws Exception {
                // Given
                String accessToken = "valid_token";
                when(workService.list(accessToken, 1, 20, null))
                                .thenReturn(ResultBean.successfulResult(mockListResult));

                // When & Then
                mockMvc.perform(get("/work/list")
                                .header("accessToken", accessToken)
                                .param("pageNumber", "1")
                                .param("pageSize", "20"))
                                .andExpect(status().isOk())
                                .andExpect(jsonPath("$.code").value("10000"));

                verify(workService).list(accessToken, 1, 20, null);
        }

        @Test
        @DisplayName("删除作品_成功场景")
        void testDelById_Success() throws Exception {
                // Given
                Long workId = 1L;
                doNothing().when(workService).delById(eq(workId), any(List.class), anyLong());

                // When & Then
                mockMvc.perform(delete("/work/{id}", workId)
                                .header("accessToken", "valid_token"))
                                .andExpect(status().isOk())
                                .andExpect(jsonPath("$.code").value("10000"))
                                .andExpect(jsonPath("$.resultData").isEmpty());

                verify(workService).delById(eq(workId), any(List.class), anyLong());
        }

        @Test
        @DisplayName("删除作品_异常场景")
        void testDelById_Exception() throws Exception {
                // Given
                Long workId = 1L;
                doThrow(new CustomException("音频文件为空")).when(workService).delById(eq(workId), any(List.class),
                                anyLong());

                // When & Then
                mockMvc.perform(delete("/work/{id}", workId)
                                .header("accessToken", "valid_token"))
                                .andExpect(status().isOk()); // 注意：这里可能需要根据实际的异常处理机制调整

                verify(workService).delById(eq(workId), any(List.class), anyLong());
        }

        @Test
        @DisplayName("获取作品信息_成功场景")
        void testInfo_Success() throws Exception {
                // Given
                Long workId = 1L;
                when(workService.info(workId)).thenReturn(ResultBean.successfulResult(mockWorkInfoVo));

                // When & Then
                mockMvc.perform(get("/work/info")
                                .param("id", workId.toString()))
                                .andExpect(status().isOk())
                                .andExpect(jsonPath("$.code").value("10000"))
                                .andExpect(jsonPath("$.resultData.id").value(1))
                                .andExpect(jsonPath("$.resultData.title").value("测试作品"));

                verify(workService).info(workId);
        }

        @Test
        @DisplayName("获取作品信息_作品不存在场景")
        void testInfo_WorkNotFound() throws Exception {
                // Given
                Long workId = 999L;
                when(workService.info(workId)).thenReturn(null);

                // When & Then
                mockMvc.perform(get("/work/info")
                                .param("id", workId.toString()))
                                .andExpect(status().isOk());

                verify(workService).info(workId);
        }

        @Test
        @DisplayName("保存作品_成功场景")
        void testSave_Success() throws Exception {
                // Given
                String accessToken = "valid_token";
                doNothing().when(workService).save(any(WorkInfoVo.class));

                // When & Then
                mockMvc.perform(post("/work/save")
                                .header("accessToken", accessToken)
                                .contentType(MediaType.APPLICATION_JSON)
                                .content(objectMapper.writeValueAsString(mockWorkInfoVo)))
                                .andExpect(status().isOk())
                                .andExpect(jsonPath("$.code").value("10000"));

                verify(workService).save(any(WorkInfoVo.class));
                verify(workController).getShopId(accessToken);
                verify(workController).getUser(accessToken);
        }

        @Test
        @DisplayName("保存作品_shopId为null场景")
        void testSave_NullShopId() throws Exception {
                // Given
                String accessToken = "invalid_token";
                doReturn(null).when(workController).getShopId(accessToken);

                // When & Then
                mockMvc.perform(post("/work/save")
                                .header("accessToken", accessToken)
                                .contentType(MediaType.APPLICATION_JSON)
                                .content(objectMapper.writeValueAsString(mockWorkInfoVo)))
                                .andExpect(status().isOk())
                                .andExpect(jsonPath("$.code").value("10000")); // 失败状态码

                verify(workController).getShopId(accessToken);
                verifyNoInteractions(workService);
        }

        @Test
        @DisplayName("保存作品_异常场景")
        void testSave_Exception() throws Exception {
                // Given
                String accessToken = "valid_token";
                doThrow(new CustomException("音频文件不能为空")).when(workService).save(any(WorkInfoVo.class));

                // When & Then
                mockMvc.perform(post("/work/save")
                                .header("accessToken", accessToken)
                                .contentType(MediaType.APPLICATION_JSON)
                                .content(objectMapper.writeValueAsString(mockWorkInfoVo)))
                                .andExpect(status().isOk()); // 注意：这里可能需要根据实际的异常处理机制调整

                verify(workService).save(any(WorkInfoVo.class));
        }

        @Test
        @DisplayName("导出功能_成功场景")
        void testInput_Success() throws Exception {
                // Given
                String url = "test-audio.mp3";
                File mockFile = mock(File.class);
                when(ossService.getObjectFile(null, url)).thenReturn(mockFile);
                when(mockFile.exists()).thenReturn(true);

                // When & Then
                mockMvc.perform(get("/work/input")
                                .param("url", url))
                                .andExpect(status().isOk());

                verify(ossService).getObjectFile(null, url);
        }

        @Test
        @DisplayName("导出功能_文件不存在场景")
        void testInput_FileNotExists() throws Exception {
                // Given
                String url = "non-existent-audio.mp3";
                when(ossService.getObjectFile(null, url)).thenReturn(null);

                // When & Then
                mockMvc.perform(get("/work/input")
                                .param("url", url))
                                .andExpect(status().isOk());

                verify(ossService).getObjectFile(null, url);
        }

        @Test
        @DisplayName("获取用户可用音频_成功场景")
        void testGetWorkList_Success() throws Exception {
                // Given
                String accessToken = "valid_token";
                when(workService.getWorkList(eq(Arrays.asList(1L)), any(WorkPageParam.class)))
                                .thenReturn(ResultBean.successfulResult(mockListResult));

                // When & Then
                mockMvc.perform(post("/work/getWorkList")
                                .header("accessToken", accessToken)
                                .contentType(MediaType.APPLICATION_JSON)
                                .content(objectMapper.writeValueAsString(mockWorkPageParam)))
                                .andExpect(status().isOk())
                                .andExpect(jsonPath("$.code").value("10000"))
                                .andExpect(jsonPath("$.resultData.total").value(1));

                verify(workController).getShopIds(accessToken);
                verify(workService).getWorkList(eq(Arrays.asList(1L)), any(WorkPageParam.class));
        }

        @Test
        @DisplayName("获取用户可用音频_shopId为null场景")
        void testGetWorkList_NullShopId() throws Exception {
                // Given
                String accessToken = "invalid_token";
                doReturn(null).when(workController).getShopIds(accessToken);
                when(workService.getWorkList(eq(null), any(WorkPageParam.class)))
                                .thenReturn(ResultBean.successfulResult(new HashMap<>()));

                // When & Then
                mockMvc.perform(post("/work/getWorkList")
                                .header("accessToken", accessToken)
                                .contentType(MediaType.APPLICATION_JSON)
                                .content(objectMapper.writeValueAsString(mockWorkPageParam)))
                                .andExpect(status().isOk())
                                .andExpect(jsonPath("$.code").value("10000"));

                verify(workController).getShopIds(accessToken);
                verify(workService).getWorkList(eq(null), any(WorkPageParam.class));
        }

        @Test
        @DisplayName("获取用户可用音频_空参数场景")
        void testGetWorkList_EmptyParam() throws Exception {
                // Given
                String accessToken = "valid_token";
                WorkPageParam emptyParam = new WorkPageParam();
                when(workService.getWorkList(eq(Arrays.asList(1L)), any(WorkPageParam.class)))
                                .thenReturn(ResultBean.successfulResult(new HashMap<>()));

                // When & Then
                mockMvc.perform(post("/work/getWorkList")
                                .header("accessToken", accessToken)
                                .contentType(MediaType.APPLICATION_JSON)
                                .content(objectMapper.writeValueAsString(emptyParam)))
                                .andExpect(status().isOk())
                                .andExpect(jsonPath("$.code").value("10000"));

                verify(workService).getWorkList(eq(Arrays.asList(1L)), any(WorkPageParam.class));
        }

        @Test
        @DisplayName("作品列表查询_服务异常场景")
        void testList_ServiceException() throws Exception {
                // Given
                String accessToken = "valid_token";
                when(workService.list(accessToken, 0, 10, null))
                                .thenReturn(ResultBean.failedResult(null));

                // When & Then
                mockMvc.perform(get("/work/list")
                                .header("accessToken", accessToken))
                                .andExpect(status().isOk())
                                .andExpect(jsonPath("$.code").value("10001"));

                verify(workService).list(accessToken, 0, 10, null);
        }

        @Test
        @DisplayName("作品列表查询_缺少accessToken场景")
        void testList_MissingAccessToken() throws Exception {
                // When & Then
                mockMvc.perform(get("/work/list"))
                                .andExpect(status().isBadRequest());

                verifyNoInteractions(workService);
        }

        @Test
        @DisplayName("保存作品_无效JSON格式场景")
        void testSave_InvalidJson() throws Exception {
                // Given
                String accessToken = "valid_token";

                // When & Then
                mockMvc.perform(post("/work/save")
                                .header("accessToken", accessToken)
                                .contentType(MediaType.APPLICATION_JSON)
                                .content("invalid json"))
                                .andExpect(status().isBadRequest());

                verifyNoInteractions(workService);
        }

        @Test
        @DisplayName("删除作品_无效ID场景")
        void testDelById_InvalidId() throws Exception {
                // When & Then
                mockMvc.perform(delete("/work/{id}", "invalid"))
                                .andExpect(status().isBadRequest());

                verifyNoInteractions(workService);
        }

        @Test
        @DisplayName("获取作品信息_缺少ID参数场景")
        void testInfo_MissingId() throws Exception {
                // When & Then
                mockMvc.perform(get("/work/info"))
                                .andExpect(status().isOk());

                verify(workService).info(null);
        }

        @Test
        @DisplayName("获取用户可用音频_缺少accessToken场景")
        void testGetWorkList_MissingAccessToken() throws Exception {
                // When & Then
                mockMvc.perform(post("/work/getWorkList")
                                .contentType(MediaType.APPLICATION_JSON)
                                .content(objectMapper.writeValueAsString(mockWorkPageParam)))
                                .andExpect(status().isBadRequest());

                verifyNoInteractions(workService);
        }

        @Test
        @DisplayName("导出功能_缺少URL参数场景")
        void testInput_MissingUrl() throws Exception {
                // When & Then
                mockMvc.perform(get("/work/input"))
                                .andExpect(status().isOk());

                verify(ossService).getObjectFile(null, null);
        }
}
