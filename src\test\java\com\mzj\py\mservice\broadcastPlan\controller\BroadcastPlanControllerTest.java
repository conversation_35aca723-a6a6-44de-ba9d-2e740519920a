package com.mzj.py.mservice.broadcastPlan.controller;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.mzj.py.commons.ResultBean;
import com.mzj.py.commons.TokenRedisVo;
import com.mzj.py.mservice.broadcastPlan.service.BroadcastPlanService;
import com.mzj.py.mservice.broadcastPlan.vo.dto.BroadcastPlanAddDto;
import com.mzj.py.mservice.common.ApiBaseController;
import com.mzj.py.mservice.redis.RedisService;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.http.MediaType;
import org.springframework.test.util.ReflectionTestUtils;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.setup.MockMvcBuilders;

import java.util.*;

import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.*;
import static org.springframework.test.web.servlet.result.MockMvcResultHandlers.print;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.*;

/**
 * BroadcastPlanController 单元测试
 * 
 * 测试覆盖：
 * 1. GET /mini/broadcastPlan - 播报计划列表查询
 * 2. POST /mini/broadcastPlan - 播报计划添加/更新
 * 3. DELETE /mini/broadcastPlan/{id} - 播报计划删除
 * 4. 请求参数验证和错误处理
 * 5. HTTP 状态码和响应格式验证
 */
@ExtendWith(MockitoExtension.class)
@DisplayName("BroadcastPlanController 单元测试")
class BroadcastPlanControllerTest {

        @Mock
        private BroadcastPlanService broadcastPlanService;
        @Mock
        private RedisService redisService;
        @InjectMocks
        private BroadcastPlanController broadcastPlanController;

        private MockMvc mockMvc;
        private ObjectMapper objectMapper;

        private static final String ACCESS_TOKEN = "test-access-token";
        private static final String BASE_URL = "/mini/broadcastPlan";

        @BeforeEach
        void setUp() {
                mockMvc = MockMvcBuilders.standaloneSetup(broadcastPlanController).build();
                objectMapper = new ObjectMapper();

                // 模拟父类方法
                BroadcastPlanController spyController = spy(broadcastPlanController);
                ReflectionTestUtils.setField(spyController, "broadcastPlanService", broadcastPlanService);
                ReflectionTestUtils.setField(spyController, "redisService", redisService);
                TokenRedisVo mockUser = new TokenRedisVo();
                mockUser.setId(1L);
                // 对 spyController 进行方法桩设置，确保调用父类方法时返回期望结果
                lenient().doReturn(Arrays.asList(1L, 2L)).when(spyController).getShopIds(ACCESS_TOKEN);
                lenient().doReturn(mockUser).when(spyController).getUser(ACCESS_TOKEN);
                lenient().doReturn(1L).when(spyController).getShopId(anyString());
                mockMvc = MockMvcBuilders.standaloneSetup(spyController)
                                .setControllerAdvice(new com.mzj.py.config.exception.GlobalExceptionHandler())
                                .build();
        }

        // ==================== GET /mini/broadcastPlan 测试 ====================

        @Test
        @DisplayName("测试播报计划列表查询 - 成功获取列表")
        void testList_Success() throws Exception {
                // Given
                Map<String, Object> mockResult = new HashMap<>();
                mockResult.put("count", 2);
                mockResult.put("result", Arrays.asList(
                                createMockPlanData(1L, "计划1"),
                                createMockPlanData(2L, "计划2")));

                ResultBean<Map<String, Object>> mockResponse = ResultBean.successfulResult(mockResult);
                when(broadcastPlanService.list(eq(Arrays.asList(1L, 2L)), eq(10), eq(0))).thenReturn(mockResponse);

                // When & Then
                mockMvc.perform(get(BASE_URL)
                                .header("accessToken", ACCESS_TOKEN)
                                .param("pageSize", "10")
                                .param("pageNumber", "0"))
                                .andDo(print())
                                .andExpect(status().isOk())
                                .andExpect(content().contentType(MediaType.APPLICATION_JSON))
                                .andExpect(jsonPath("$.code").value("10000"))
                                .andExpect(jsonPath("$.resultData.count").value(2))
                                .andExpect(jsonPath("$.resultData.result").isArray())
                                .andExpect(jsonPath("$.resultData.result.length()").value(2));

                // 验证服务方法被调用
                verify(broadcastPlanService, times(1)).list(Arrays.asList(1L, 2L), 10, 0);
        }

        @Test
        @DisplayName("测试播报计划列表查询 - 使用默认分页参数")
        void testList_DefaultPagination() throws Exception {
                // Given
                Map<String, Object> mockResult = new HashMap<>();
                mockResult.put("count", 0);
                mockResult.put("result", Collections.emptyList());

                ResultBean<Map<String, Object>> mockResponse = ResultBean.successfulResult(mockResult);
                when(broadcastPlanService.list(eq(Arrays.asList(1L, 2L)), eq(10), eq(0))).thenReturn(mockResponse);

                // When & Then
                mockMvc.perform(get(BASE_URL)
                                .header("accessToken", ACCESS_TOKEN))
                                .andDo(print())
                                .andExpect(status().isOk())
                                .andExpect(jsonPath("$.code").value("10000"))
                                .andExpect(jsonPath("$.resultData.count").value(0))
                                .andExpect(jsonPath("$.resultData.result").isArray())
                                .andExpect(jsonPath("$.resultData.result.length()").value(0));

                // 验证使用了默认参数
                verify(broadcastPlanService, times(1)).list(Arrays.asList(1L, 2L), 10, 0);
        }

        @Test
        @DisplayName("测试播报计划列表查询 - 自定义分页参数")
        void testList_CustomPagination() throws Exception {
                // Given
                Map<String, Object> mockResult = new HashMap<>();
                mockResult.put("count", 50);
                mockResult.put("result", Collections.emptyList());

                ResultBean<Map<String, Object>> mockResponse = ResultBean.successfulResult(mockResult);
                when(broadcastPlanService.list(eq(Arrays.asList(1L, 2L)), eq(20), eq(2))).thenReturn(mockResponse);

                // When & Then
                mockMvc.perform(get(BASE_URL)
                                .header("accessToken", ACCESS_TOKEN)
                                .param("pageSize", "20")
                                .param("pageNumber", "2"))
                                .andDo(print())
                                .andExpect(status().isOk())
                                .andExpect(jsonPath("$.resultData.count").value(50));

                // 验证使用了自定义参数
                verify(broadcastPlanService, times(1)).list(Arrays.asList(1L, 2L), 20, 2);
        }

        @Test
        @DisplayName("测试播报计划列表查询 - shopId为null")
        void testList_NullShopId() throws Exception {
                // Given - 模拟 getShopId 返回 null
                BroadcastPlanController spyController = spy(broadcastPlanController);
                doReturn(null).when(spyController).getShopId(anyString());
                ReflectionTestUtils.setField(spyController, "broadcastPlanService", broadcastPlanService);

                mockMvc = MockMvcBuilders.standaloneSetup(spyController).build();

                // When & Then
                mockMvc.perform(get(BASE_URL)
                                .header("accessToken", ACCESS_TOKEN))
                                .andDo(print())
                                .andExpect(status().isOk())
                                .andExpect(jsonPath("$.code").value("10000"))
                                .andExpect(jsonPath("$.resultData.count").value(0))
                                .andExpect(jsonPath("$.resultData.result").isArray())
                                .andExpect(jsonPath("$.resultData.result.length()").value(0));

                // 验证服务方法没有被调用
                verify(broadcastPlanService, never()).list(any(), any(), any());
        }

        // ==================== POST /mini/broadcastPlan 测试 ====================

        @Test
        @DisplayName("测试添加播报计划 - 成功添加")
        void testAddOrUpdate_AddSuccess() throws Exception {
                // Given
                BroadcastPlanAddDto requestDto = createMockAddDto();
                requestDto.setId(null); // 新增场景

                ResultBean<Boolean> mockResponse = ResultBean.successfulResult(true);
                when(broadcastPlanService.addOrUpdate(any(BroadcastPlanAddDto.class), eq(ACCESS_TOKEN)))
                                .thenReturn(mockResponse);

                // When & Then
                mockMvc.perform(post(BASE_URL)
                                .header("accessToken", ACCESS_TOKEN)
                                .contentType(MediaType.APPLICATION_JSON)
                                .content(objectMapper.writeValueAsString(requestDto)))
                                .andDo(print())
                                .andExpect(status().isOk())
                                .andExpect(content().contentType(MediaType.APPLICATION_JSON))
                                .andExpect(jsonPath("$.code").value("10000"))
                                .andExpect(jsonPath("$.resultData").value(true));

                // 验证服务方法被调用，并且 shopId 被设置
                verify(broadcastPlanService, times(1))
                                .addOrUpdate(argThat(dto -> dto.getShopId().equals(1L) && dto.getId() == null),
                                                eq(ACCESS_TOKEN));
        }

        @Test
        @DisplayName("测试更新播报计划 - 成功更新")
        void testAddOrUpdate_UpdateSuccess() throws Exception {
                // Given
                BroadcastPlanAddDto requestDto = createMockAddDto();
                requestDto.setId(1L); // 更新场景

                ResultBean<Boolean> mockResponse = ResultBean.successfulResult(true);
                when(broadcastPlanService.addOrUpdate(any(BroadcastPlanAddDto.class), eq(ACCESS_TOKEN)))
                                .thenReturn(mockResponse);

                // When & Then
                mockMvc.perform(post(BASE_URL)
                                .header("accessToken", ACCESS_TOKEN)
                                .contentType(MediaType.APPLICATION_JSON)
                                .content(objectMapper.writeValueAsString(requestDto)))
                                .andDo(print())
                                .andExpect(status().isOk())
                                .andExpect(jsonPath("$.code").value("10000"))
                                .andExpect(jsonPath("$.resultData").value(true));

                // 验证服务方法被调用
                verify(broadcastPlanService, times(1))
                                .addOrUpdate(argThat(dto -> dto.getShopId().equals(1L) && dto.getId().equals(1L)),
                                                eq(ACCESS_TOKEN));
        }

        @Test
        @DisplayName("测试添加播报计划 - 业务逻辑失败")
        void testAddOrUpdate_BusinessFailure() throws Exception {
                // Given
                BroadcastPlanAddDto requestDto = createMockAddDto();

                ResultBean<Boolean> mockResponse = ResultBean.failedResultWithMsg("播报开始时段不能为空");
                when(broadcastPlanService.addOrUpdate(any(BroadcastPlanAddDto.class), eq(ACCESS_TOKEN)))
                                .thenReturn(mockResponse);

                // When & Then
                mockMvc.perform(post(BASE_URL)
                                .header("accessToken", ACCESS_TOKEN)
                                .contentType(MediaType.APPLICATION_JSON)
                                .content(objectMapper.writeValueAsString(requestDto)))
                                .andDo(print())
                                .andExpect(status().isOk())
                                .andExpect(jsonPath("$.code").value("10001"))
                                .andExpect(jsonPath("$.msg").value("播报开始时段不能为空"));

                verify(broadcastPlanService, times(1)).addOrUpdate(any(BroadcastPlanAddDto.class), eq(ACCESS_TOKEN));
        }

        @Test
        @DisplayName("测试添加播报计划 - 请求体为空")
        void testAddOrUpdate_EmptyRequestBody() throws Exception {
                // When & Then
                mockMvc.perform(post(BASE_URL)
                                .header("accessToken", ACCESS_TOKEN)
                                .contentType(MediaType.APPLICATION_JSON)
                                .content("{}"))
                                .andDo(print())
                                .andExpect(status().isOk());

                // 验证服务方法被调用（空对象也会传递给服务层处理）
                verify(broadcastPlanService, times(1)).addOrUpdate(any(BroadcastPlanAddDto.class), eq(ACCESS_TOKEN));
        }

        @Test
        @DisplayName("测试添加播报计划 - 无效JSON格式")
        void testAddOrUpdate_InvalidJson() throws Exception {
                // When & Then
                mockMvc.perform(post(BASE_URL)
                                .header("accessToken", ACCESS_TOKEN)
                                .contentType(MediaType.APPLICATION_JSON)
                                .content("invalid json"))
                                .andDo(print())
                                .andExpect(status().isBadRequest());

                // 验证服务方法没有被调用
                verify(broadcastPlanService, never()).addOrUpdate(any(BroadcastPlanAddDto.class), anyString());
        }

        // ==================== DELETE /mini/broadcastPlan/{id} 测试 ====================

        @Test
        @DisplayName("测试删除播报计划 - 成功删除")
        void testDelete_Success() throws Exception {
                // Given
                Long planId = 1L;
                ResultBean<Boolean> mockResponse = ResultBean.successfulResult(true);
                when(broadcastPlanService.delete(planId)).thenReturn(mockResponse);

                // When & Then
                mockMvc.perform(delete(BASE_URL + "/" + planId))
                                .andDo(print())
                                .andExpect(status().isOk())
                                .andExpect(content().contentType(MediaType.APPLICATION_JSON))
                                .andExpect(jsonPath("$.code").value("10000"))
                                .andExpect(jsonPath("$.resultData").value(true));

                // 验证服务方法被调用
                verify(broadcastPlanService, times(1)).delete(planId);
        }

        @Test
        @DisplayName("测试删除播报计划 - 删除不存在的计划")
        void testDelete_NonExistentPlan() throws Exception {
                // Given
                Long planId = 999L;
                ResultBean<Boolean> mockResponse = ResultBean.successfulResult(true);
                when(broadcastPlanService.delete(planId)).thenReturn(mockResponse);

                // When & Then
                mockMvc.perform(delete(BASE_URL + "/" + planId))
                                .andDo(print())
                                .andExpect(status().isOk())
                                .andExpect(jsonPath("$.code").value("10000"))
                                .andExpect(jsonPath("$.resultData").value(true));

                verify(broadcastPlanService, times(1)).delete(planId);
        }

        @Test
        @DisplayName("测试删除播报计划 - 无效的ID格式")
        void testDelete_InvalidIdFormat() throws Exception {
                // When & Then
                mockMvc.perform(delete(BASE_URL + "/invalid-id"))
                                .andDo(print())
                                .andExpect(status().isBadRequest());

                // 验证服务方法没有被调用
                verify(broadcastPlanService, never()).delete(any());
        }

        // ==================== 异常和边缘情况测试 ====================

        @Test
        @DisplayName("测试缺少 accessToken 请求头")
        void testMissingAccessToken() throws Exception {
                // When & Then
                mockMvc.perform(get(BASE_URL))
                                .andDo(print())
                                .andExpect(status().isBadRequest());

                verify(broadcastPlanService, never()).list(any(), any(), any());
        }

        @Test
        @DisplayName("测试服务层抛出异常")
        void testServiceException() throws Exception {
                // Given
                when(broadcastPlanService.list(any(), any(), any()))
                                .thenThrow(new RuntimeException("数据库连接异常"));

                // When & Then
                mockMvc.perform(get(BASE_URL)
                                .header("accessToken", ACCESS_TOKEN))
                                .andDo(print())
                                .andExpect(status().isInternalServerError());

                verify(broadcastPlanService, times(1)).list(any(), any(), any());
        }

        @Test
        @DisplayName("测试添加播报计划 - 服务层抛出异常")
        void testAddOrUpdate_ServiceException() throws Exception {
                // Given
                BroadcastPlanAddDto requestDto = createMockAddDto();
                when(broadcastPlanService.addOrUpdate(any(BroadcastPlanAddDto.class), anyString()))
                                .thenThrow(new RuntimeException("保存失败"));

                // When & Then
                mockMvc.perform(post(BASE_URL)
                                .header("accessToken", ACCESS_TOKEN)
                                .contentType(MediaType.APPLICATION_JSON)
                                .content(objectMapper.writeValueAsString(requestDto)))
                                .andDo(print())
                                .andExpect(status().isInternalServerError());

                verify(broadcastPlanService, times(1)).addOrUpdate(any(BroadcastPlanAddDto.class), anyString());
        }

        @Test
        @DisplayName("测试删除播报计划 - 服务层抛出异常")
        void testDelete_ServiceException() throws Exception {
                // Given
                Long planId = 1L;
                when(broadcastPlanService.delete(planId))
                                .thenThrow(new RuntimeException("删除失败"));

                // When & Then
                mockMvc.perform(delete(BASE_URL + "/" + planId))
                                .andDo(print())
                                .andExpect(status().isInternalServerError());

                verify(broadcastPlanService, times(1)).delete(planId);
        }

        @Test
        @DisplayName("测试添加播报计划 - 复杂数据结构")
        void testAddOrUpdate_ComplexData() throws Exception {
                // Given
                BroadcastPlanAddDto requestDto = new BroadcastPlanAddDto();
                requestDto.setDeviceIds(Arrays.asList(1L, 2L, 3L, 4L, 5L));
                requestDto.setStartTime("06:30");
                requestDto.setEndTime("23:59");
                requestDto.setStartDate("2024-01-01");
                requestDto.setEndDate("2024-12-31");
                requestDto.setType("2"); // 自定义日期类型
                requestDto.setIntervalTime("15");

                // 大量语音作品
                List<Long> voiceWorkList = new ArrayList<>();
                for (long i = 1; i <= 50; i++) {
                        voiceWorkList.add(i);
                }
                requestDto.setVoiceWorkList(voiceWorkList);

                ResultBean<Boolean> mockResponse = ResultBean.successfulResult(true);
                when(broadcastPlanService.addOrUpdate(any(BroadcastPlanAddDto.class), eq(ACCESS_TOKEN)))
                                .thenReturn(mockResponse);

                // When & Then
                mockMvc.perform(post(BASE_URL)
                                .header("accessToken", ACCESS_TOKEN)
                                .contentType(MediaType.APPLICATION_JSON)
                                .content(objectMapper.writeValueAsString(requestDto)))
                                .andDo(print())
                                .andExpect(status().isOk())
                                .andExpect(jsonPath("$.code").value("10000"))
                                .andExpect(jsonPath("$.resultData").value(true));

                // 验证复杂数据被正确传递
                verify(broadcastPlanService, times(1)).addOrUpdate(argThat(dto -> dto.getDeviceIds().size() == 5 &&
                                dto.getVoiceWorkList().size() == 50 &&
                                dto.getType().equals("2")), eq(ACCESS_TOKEN));
        }

        @Test
        @DisplayName("测试分页参数边界值")
        void testList_BoundaryPaginationValues() throws Exception {
                // Given
                Map<String, Object> mockResult = new HashMap<>();
                mockResult.put("count", 0);
                mockResult.put("result", Collections.emptyList());

                ResultBean<Map<String, Object>> mockResponse = ResultBean.successfulResult(mockResult);
                when(broadcastPlanService.list(eq(Arrays.asList(1L, 2L)), eq(1), eq(0))).thenReturn(mockResponse);

                // When & Then - 测试最小分页值
                mockMvc.perform(get(BASE_URL)
                                .header("accessToken", ACCESS_TOKEN)
                                .param("pageSize", "1")
                                .param("pageNumber", "0"))
                                .andDo(print())
                                .andExpect(status().isOk())
                                .andExpect(jsonPath("$.code").value("10000"));

                verify(broadcastPlanService, times(1)).list(Arrays.asList(1L, 2L), 1, 0);
        }

        @Test
        @DisplayName("测试大分页参数值")
        void testList_LargePaginationValues() throws Exception {
                // Given
                Map<String, Object> mockResult = new HashMap<>();
                mockResult.put("count", 10000);
                mockResult.put("result", Collections.emptyList());

                ResultBean<Map<String, Object>> mockResponse = ResultBean.successfulResult(mockResult);
                when(broadcastPlanService.list(eq(Arrays.asList(1L, 2L)), eq(1000), eq(100))).thenReturn(mockResponse);

                // When & Then - 测试大分页值
                mockMvc.perform(get(BASE_URL)
                                .header("accessToken", ACCESS_TOKEN)
                                .param("pageSize", "1000")
                                .param("pageNumber", "100"))
                                .andDo(print())
                                .andExpect(status().isOk())
                                .andExpect(jsonPath("$.code").value("10000"))
                                .andExpect(jsonPath("$.resultData.count").value(10000));

                verify(broadcastPlanService, times(1)).list(Arrays.asList(1L, 2L), 1000, 100);
        }

        @Test
        @DisplayName("测试删除播报计划 - 零ID")
        void testDelete_ZeroId() throws Exception {
                // Given
                Long planId = 0L;
                ResultBean<Boolean> mockResponse = ResultBean.successfulResult(true);
                when(broadcastPlanService.delete(planId)).thenReturn(mockResponse);

                // When & Then
                mockMvc.perform(delete(BASE_URL + "/" + planId))
                                .andDo(print())
                                .andExpect(status().isOk())
                                .andExpect(jsonPath("$.code").value("10000"));

                verify(broadcastPlanService, times(1)).delete(planId);
        }

        @Test
        @DisplayName("测试删除播报计划 - 负数ID")
        void testDelete_NegativeId() throws Exception {
                // Given
                Long planId = -1L;
                ResultBean<Boolean> mockResponse = ResultBean.successfulResult(true);
                when(broadcastPlanService.delete(planId)).thenReturn(mockResponse);

                // When & Then
                mockMvc.perform(delete(BASE_URL + "/" + planId))
                                .andDo(print())
                                .andExpect(status().isOk())
                                .andExpect(jsonPath("$.code").value("10000"));

                verify(broadcastPlanService, times(1)).delete(planId);
        }

        // ==================== 辅助方法 ====================

        private BroadcastPlanAddDto createMockAddDto() {
                BroadcastPlanAddDto dto = new BroadcastPlanAddDto();
                dto.setDeviceIds(Arrays.asList(1L, 2L));
                dto.setStartTime("09:00");
                dto.setEndTime("18:00");
                dto.setStartDate("2024-01-01");
                dto.setEndDate("2024-12-31");
                dto.setType("1");
                dto.setIntervalTime("30");
                dto.setVoiceWorkList(Arrays.asList(1L, 2L));
                return dto;
        }

        private Map<String, Object> createMockPlanData(Long id, String name) {
                Map<String, Object> plan = new HashMap<>();
                plan.put("id", id);
                plan.put("store", name);
                plan.put("startTime", "09:00");
                plan.put("endTime", "18:00");
                plan.put("deviceIds", "1,2");
                plan.put("musicIds", "1,2");
                return plan;
        }
}
