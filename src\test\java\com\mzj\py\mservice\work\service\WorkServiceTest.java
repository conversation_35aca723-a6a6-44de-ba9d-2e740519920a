package com.mzj.py.mservice.work.service;

import com.mzj.py.commons.ResultBean;
import com.mzj.py.commons.StringUtils;
import com.mzj.py.commons.TokenRedisVo;
import com.mzj.py.commons.exception.CustomException;
import com.mzj.py.mservice.home.entity.DeviceVoice;
import com.mzj.py.mservice.home.entity.VoicePacket;
import com.mzj.py.mservice.home.entity.VoiceWork;
import com.mzj.py.mservice.home.repository.DeviceVoiceRepository;
import com.mzj.py.mservice.home.repository.VoicePacketRepository;
import com.mzj.py.mservice.home.repository.VoiceWorkRepository;
import com.mzj.py.mservice.oss.service.OSSService;
import com.mzj.py.mservice.redis.RedisService;
import com.mzj.py.mservice.work.vo.WorkInfoVo;
import com.mzj.py.mservice.work.vo.WorkPageParam;
import com.mzj.py.mservice.work.vo.WorkVo;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.jdbc.core.BeanPropertyRowMapper;
import org.springframework.jdbc.core.JdbcTemplate;

import java.util.*;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

/**
 * WorkService单元测试类
 * 测试WorkService中所有公共方法的正面和负面场景
 */
@ExtendWith(MockitoExtension.class)
@DisplayName("WorkService单元测试")
class WorkServiceTest {

    @Mock
    private RedisService redisService;

    @Mock
    private JdbcTemplate jdbcTemplate;

    @Mock
    private VoiceWorkRepository workRepository;

    @Mock
    private VoicePacketRepository packetRepository;

    @Mock
    private DeviceVoiceRepository voiceRepository;

    @Mock
    private OSSService ossService;

    @InjectMocks
    private WorkService workService;

    private TokenRedisVo mockUser;
    private VoiceWork mockVoiceWork;
    private VoicePacket mockVoicePacket;
    private WorkInfoVo mockWorkInfoVo;
    private WorkPageParam mockWorkPageParam;

    @BeforeEach
    void setUp() {
        // 初始化测试数据
        mockUser = new TokenRedisVo();
        mockUser.setId(1L);
        mockUser.setPhone("13800138000");
        mockUser.setNicknames("测试用户");

        mockVoiceWork = new VoiceWork();
        mockVoiceWork.setId(1L);
        mockVoiceWork.setTitle("测试作品");
        mockVoiceWork.setContent("测试内容");
        mockVoiceWork.setUserId(1L);
        mockVoiceWork.setShopId(1L);
        mockVoiceWork.setDelStatus(0);
        mockVoiceWork.setCreateTime(new Date());

        mockVoicePacket = new VoicePacket();
        mockVoicePacket.setId(1L);
        mockVoicePacket.setVoiceWorkId(1L);
        mockVoicePacket.setFileUrl("http://test.com/audio.mp3");
        mockVoicePacket.setName("测试语音包");
        mockVoicePacket.setVoiceTime(60);

        mockWorkInfoVo = new WorkInfoVo();
        mockWorkInfoVo.setId(1L);
        mockWorkInfoVo.setTitle("测试作品");
        mockWorkInfoVo.setContent("测试内容");
        mockWorkInfoVo.setFileUrl("http://test.com/audio.mp3");
        mockWorkInfoVo.setUserId(1L);
        mockWorkInfoVo.setShopId(1L);
        mockWorkInfoVo.setVoiceTime(60);
        mockWorkInfoVo.setSpeed(50);
        mockWorkInfoVo.setVolume(80);
        mockWorkInfoVo.setPitch(50);

        mockWorkPageParam = new WorkPageParam();
        mockWorkPageParam.setPageNumber(0);
        mockWorkPageParam.setPageSize(10);
        mockWorkPageParam.setKeyword("测试");
    }

    @Test
    @DisplayName("获取用户信息_成功场景")
    void testGetUser_Success() {
        // Given
        String accessToken = "valid_token";
        when(redisService.findTokenVo(accessToken)).thenReturn(mockUser);

        // When
        ResultBean<TokenRedisVo> result = workService.getUser(accessToken);

        // Then
        assertNotNull(result);
        assertEquals("10000", result.getCode());
        assertEquals(mockUser, result.getResultData());
        verify(redisService).findTokenVo(accessToken);
    }

    @Test
    @DisplayName("获取用户信息_Token无效场景")
    void testGetUser_InvalidToken() {
        // Given
        String accessToken = "invalid_token";
        when(redisService.findTokenVo(accessToken)).thenReturn(null);

        // When
        ResultBean<TokenRedisVo> result = workService.getUser(accessToken);

        // Then
        assertNotNull(result);
        assertEquals("40001", result.getCode()); // 修正为实际的token失效状态码
        assertNull(result.getResultData());
        verify(redisService).findTokenVo(accessToken);
    }

    @Test
    @DisplayName("作品列表查询_成功场景")
    void testList_Success() {
        // Given
        String accessToken = "valid_token";
        Integer pageNumber = 0;
        Integer pageSize = 10;
        String keyword = "测试";

        when(redisService.findTokenVo(accessToken)).thenReturn(mockUser);

        // 使用lenient()来避免严格的参数匹配问题
        lenient().when(jdbcTemplate.queryForObject(anyString(), eq(Long.class), (Object[]) any())).thenReturn(1L);

        List<WorkVo> workList = Arrays.asList(createMockWorkVo());
        lenient().when(jdbcTemplate.query(anyString(), any(BeanPropertyRowMapper.class), (Object[]) any()))
                .thenReturn(workList);

        // When
        ResultBean<Map<String, Object>> result = workService.list(accessToken, pageNumber, pageSize, keyword);

        // Then
        assertNotNull(result);
        assertEquals("10000", result.getCode());
        assertNotNull(result.getResultData());
        assertTrue(result.getResultData().containsKey("total"));
        assertTrue(result.getResultData().containsKey("result"));
        assertEquals(1L, result.getResultData().get("total"));
        assertEquals(workList, result.getResultData().get("result"));

        verify(redisService).findTokenVo(accessToken);
    }

    @Test
    @DisplayName("作品列表查询_用户不存在场景")
    void testList_UserNotFound() {
        // Given
        String accessToken = "invalid_token";
        when(redisService.findTokenVo(accessToken)).thenReturn(null);

        // When
        ResultBean<Map<String, Object>> result = workService.list(accessToken, 0, 10, null);

        // Then
        assertNotNull(result);
        assertEquals("40001", result.getCode()); // 修正为实际的token失效状态码
        verify(redisService).findTokenVo(accessToken);
        verifyNoInteractions(jdbcTemplate);
    }

    @Test
    @DisplayName("作品列表查询_无关键字场景")
    void testList_WithoutKeyword() {
        // Given
        String accessToken = "valid_token";
        when(redisService.findTokenVo(accessToken)).thenReturn(mockUser);

        lenient().when(jdbcTemplate.queryForObject(anyString(), eq(Long.class), any(Object[].class))).thenReturn(0L);

        lenient().when(jdbcTemplate.query(anyString(), any(BeanPropertyRowMapper.class), (Object[]) any()))
                .thenReturn(Collections.emptyList());

        // When
        ResultBean<Map<String, Object>> result = workService.list(accessToken, 0, 10, null);

        // Then
        assertNotNull(result);
        assertEquals("10000", result.getCode());
        assertEquals(0L, result.getResultData().get("total"));
        assertNull(result.getResultData().get("result"));
    }

    @Test
    @DisplayName("删除作品_成功场景")
    void testDelById_Success() throws CustomException {
        // Given
        Long workId = 1L;
        when(packetRepository.findByVoiceWorkId(workId)).thenReturn(mockVoicePacket);
        when(ossService.doesObjectExist(mockVoicePacket.getFileUrl())).thenReturn(true);

        // When
        assertDoesNotThrow(() -> workService.delById(workId, Arrays.asList(1L), 1L));

        // Then
        verify(packetRepository).findByVoiceWorkId(workId);
        verify(ossService).doesObjectExist(mockVoicePacket.getFileUrl());
        verify(ossService).deleteObject(null, mockVoicePacket.getFileUrl());
        verify(workRepository).editById(workId, Arrays.asList(1L), 1L);
        verify(packetRepository).deleteByVoiceWorkId(workId);
    }

    @Test
    @DisplayName("删除作品_音频文件为空异常")
    void testDelById_EmptyFileUrl() {
        // Given
        Long workId = 1L;
        VoicePacket emptyPacket = new VoicePacket();
        emptyPacket.setFileUrl("");
        when(packetRepository.findByVoiceWorkId(workId)).thenReturn(emptyPacket);

        // When & Then
        assertDoesNotThrow(() -> workService.delById(workId, Arrays.asList(1L), 1L));

        verify(packetRepository).findByVoiceWorkId(workId);
        verifyNoInteractions(ossService);
        verifyNoInteractions(workRepository);
    }

    @Test
    @DisplayName("删除作品_文件不存在场景")
    void testDelById_FileNotExists() throws CustomException {
        // Given
        Long workId = 1L;
        when(packetRepository.findByVoiceWorkId(workId)).thenReturn(mockVoicePacket);
        when(ossService.doesObjectExist(mockVoicePacket.getFileUrl())).thenReturn(false);

        // When
        assertDoesNotThrow(() -> workService.delById(workId, Arrays.asList(1L), 1L));

        // Then
        verify(packetRepository).findByVoiceWorkId(workId);
        verify(ossService).doesObjectExist(mockVoicePacket.getFileUrl());
        verify(ossService, never()).deleteObject(any(), any());
        verify(workRepository).editById(workId, Arrays.asList(1L), 1L);
        verify(packetRepository).deleteByVoiceWorkId(workId);
    }

    @Test
    @DisplayName("获取作品信息_成功场景")
    void testInfo_Success() {
        // Given
        Long workId = 1L;
        List<WorkInfoVo> workInfoList = Arrays.asList(mockWorkInfoVo);
        when(jdbcTemplate.query(anyString(), any(BeanPropertyRowMapper.class), eq(workId)))
                .thenReturn(workInfoList);

        // When
        ResultBean<WorkInfoVo> result = workService.info(workId);

        // Then
        assertNotNull(result);
        assertEquals("10000", result.getCode());
        assertEquals(mockWorkInfoVo, result.getResultData());
        verify(jdbcTemplate).query(anyString(), any(BeanPropertyRowMapper.class), eq(workId));
    }

    @Test
    @DisplayName("获取作品信息_作品不存在场景")
    void testInfo_WorkNotFound() {
        // Given
        Long workId = 999L;
        when(jdbcTemplate.query(anyString(), any(BeanPropertyRowMapper.class), eq(workId)))
                .thenReturn(Collections.emptyList());

        // When
        ResultBean<WorkInfoVo> result = workService.info(workId);

        // Then
        assertNull(result);
        verify(jdbcTemplate).query(anyString(), any(BeanPropertyRowMapper.class), eq(workId));
    }

    @Test
    @DisplayName("保存作品_新增成功场景")
    void testSave_CreateSuccess() throws CustomException {
        // Given
        mockWorkInfoVo.setId(null); // 新增场景
        when(workRepository.save(any(VoiceWork.class))).thenReturn(mockVoiceWork);
        when(packetRepository.findByVoiceWorkId(any())).thenReturn(null);
        when(packetRepository.save(any(VoicePacket.class))).thenReturn(mockVoicePacket);

        // When
        assertDoesNotThrow(() -> workService.save(mockWorkInfoVo));

        // Then
        verify(workRepository, times(2)).save(any(VoiceWork.class)); // 实际调用了2次save
        verify(packetRepository).findByVoiceWorkId(any());
        verify(packetRepository).save(any(VoicePacket.class));
    }

    @Test
    @DisplayName("保存作品_更新成功场景")
    void testSave_UpdateSuccess() throws CustomException {
        // Given
        when(workRepository.getOne(mockWorkInfoVo.getId())).thenReturn(mockVoiceWork);
        when(workRepository.save(any(VoiceWork.class))).thenReturn(mockVoiceWork);
        when(packetRepository.findByVoiceWorkId(mockWorkInfoVo.getId())).thenReturn(mockVoicePacket);
        when(packetRepository.save(any(VoicePacket.class))).thenReturn(mockVoicePacket);

        // When
        assertDoesNotThrow(() -> workService.save(mockWorkInfoVo));

        // Then
        verify(workRepository).getOne(mockWorkInfoVo.getId());
        verify(workRepository, times(2)).save(any(VoiceWork.class)); // 实际调用了2次save
        verify(packetRepository).findByVoiceWorkId(mockWorkInfoVo.getId());
        verify(packetRepository).save(any(VoicePacket.class));
    }

    @Test
    @DisplayName("保存作品_音频文件为空异常")
    void testSave_EmptyFileUrl() {
        // Given
        mockWorkInfoVo.setFileUrl("");

        // When & Then
        CustomException exception = assertThrows(CustomException.class,
                () -> workService.save(mockWorkInfoVo));
        assertEquals("音频文件不能为空", exception.getMessage());

        verifyNoInteractions(workRepository);
        verifyNoInteractions(packetRepository);
    }

    @Test
    @DisplayName("保存作品_音频文件为null异常")
    void testSave_NullFileUrl() {
        // Given
        mockWorkInfoVo.setFileUrl(null);

        // When & Then
        CustomException exception = assertThrows(CustomException.class,
                () -> workService.save(mockWorkInfoVo));
        assertEquals("音频文件不能为空", exception.getMessage());

        verifyNoInteractions(workRepository);
        verifyNoInteractions(packetRepository);
    }

    @Test
    @DisplayName("获取用户可用音频列表_成功场景")
    void testGetWorkList_Success() {
        // Given
        List<Long> shopIds = Arrays.asList(1L);
        List<VoiceWork> workList = Arrays.asList(mockVoiceWork);
        Page<VoiceWork> workPage = new PageImpl<>(workList, PageRequest.of(0, 10), 1);

        when(workRepository.findAll(any(Specification.class), any(Pageable.class)))
                .thenReturn(workPage);

        // When
        ResultBean<Map<String, Object>> result = workService.getWorkList(shopIds, mockWorkPageParam);

        // Then
        assertNotNull(result);
        assertEquals("10000", result.getCode());
        assertNotNull(result.getResultData());
        Map<String, Object> resultData = result.getResultData();
        assertNotNull(resultData.get("total"));
        assertNotNull(resultData.get("result"));
        assertEquals(1, resultData.get("total"));
        assertEquals(workList, resultData.get("result"));

        verify(workRepository).findAll(any(Specification.class), any(Pageable.class));
    }

    @Test
    @DisplayName("获取用户可用音频列表_无关键字场景")
    void testGetWorkList_WithoutKeyword() {
        // Given
        List<Long> shopIds = Arrays.asList(1L);
        mockWorkPageParam.setKeyword(null);
        List<VoiceWork> workList = Arrays.asList(mockVoiceWork);
        Page<VoiceWork> workPage = new PageImpl<>(workList, PageRequest.of(0, 10), 1);

        when(workRepository.findAll(any(Specification.class), any(Pageable.class)))
                .thenReturn(workPage);

        // When
        ResultBean<Map<String, Object>> result = workService.getWorkList(shopIds, mockWorkPageParam);

        // Then
        assertNotNull(result);
        assertEquals("10000", result.getCode());
        verify(workRepository).findAll(any(Specification.class), any(Pageable.class));
    }

    @Test
    @DisplayName("获取用户可用音频列表_shopId为null场景")
    void testGetWorkList_NullShopId() {
        // Given
        List<Long> shopIds = null;
        List<VoiceWork> workList = Collections.emptyList();
        Page<VoiceWork> workPage = new PageImpl<>(workList, PageRequest.of(0, 10), 0);

        when(workRepository.findAll(any(Specification.class), any(Pageable.class)))
                .thenReturn(workPage);

        // When
        ResultBean<Map<String, Object>> result = workService.getWorkList(shopIds, mockWorkPageParam);

        // Then
        assertNotNull(result);
        assertEquals("10000", result.getCode());
        Map<String, Object> resultData = result.getResultData();
        assertNotNull(resultData);
        assertEquals(0, resultData.get("total"));
        verify(workRepository).findAll(any(Specification.class), any(Pageable.class));
    }

    @Test
    @DisplayName("获取用户可用音频列表_空结果场景")
    void testGetWorkList_EmptyResult() {
        // Given
        List<Long> shopIds = Arrays.asList(1L);
        List<VoiceWork> workList = Collections.emptyList();
        Page<VoiceWork> workPage = new PageImpl<>(workList, PageRequest.of(0, 10), 0);

        when(workRepository.findAll(any(Specification.class), any(Pageable.class)))
                .thenReturn(workPage);

        // When
        ResultBean<Map<String, Object>> result = workService.getWorkList(shopIds, mockWorkPageParam);

        // Then
        assertNotNull(result);
        assertEquals("10000", result.getCode());
        Map<String, Object> resultData = result.getResultData();
        assertNotNull(resultData);
        assertEquals(0, resultData.get("total"));
        assertTrue(((List<?>) resultData.get("result")).isEmpty());
        verify(workRepository).findAll(any(Specification.class), any(Pageable.class));
    }

    private WorkVo createMockWorkVo() {
        WorkVo workVo = new WorkVo();
        workVo.setId(1L);
        workVo.setTitle("测试作品");
        workVo.setFileUrl("http://test.com/audio.mp3");
        workVo.setVoiceTime(60);
        workVo.setCreateTime(new Date());
        return workVo;
    }
}
